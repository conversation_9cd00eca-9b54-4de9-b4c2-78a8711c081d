using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Session;

/// <summary>
/// 会话清理后台服务
/// </summary>
public class SessionCleanupBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SessionCleanupBackgroundService> _logger;
    private readonly SessionManagerOptions _options;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="options">配置选项</param>
    /// <param name="logger">日志记录器</param>
    public SessionCleanupBackgroundService(
        IServiceProvider serviceProvider,
        IOptions<SessionManagerOptions> options,
        ILogger<SessionCleanupBackgroundService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Session cleanup background service started");

        if (!_options.EnableAutomaticCleanup)
        {
            _logger.LogInformation("Automatic cleanup is disabled, service will not perform cleanup");
            return;
        }

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformCleanupAsync(stoppingToken);
                
                // 等待下一次清理
                var delay = TimeSpan.FromMinutes(_options.CleanupIntervalMinutes);
                _logger.LogTrace("Next cleanup scheduled in {Delay} minutes", delay.TotalMinutes);
                
                await Task.Delay(delay, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // 正常停止
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during session cleanup");
                
                // 发生错误时等待较短时间后重试
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        _logger.LogInformation("Session cleanup background service stopped");
    }

    /// <summary>
    /// 执行清理任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var sessionManager = scope.ServiceProvider.GetRequiredService<ISessionManager>();

        var startTime = DateTime.UtcNow;
        _logger.LogDebug("Starting session cleanup at {StartTime}", startTime);

        try
        {
            var result = await sessionManager.CleanupExpiredSessionsAsync(cancellationToken);

            var duration = DateTime.UtcNow - startTime;
            
            if (result.IsSuccess)
            {
                if (result.TotalCleanedRecords > 0)
                {
                    _logger.LogInformation(
                        "Session cleanup completed successfully: {SessionsCount} sessions, {SubscriptionsCount} subscriptions, {MessagesCount} messages cleaned in {Duration}ms",
                        result.CleanedSessionsCount,
                        result.CleanedSubscriptionsCount,
                        result.CleanedPendingMessagesCount,
                        duration.TotalMilliseconds);
                }
                else
                {
                    _logger.LogTrace("Session cleanup completed: no expired sessions found");
                }
            }
            else
            {
                _logger.LogWarning("Session cleanup failed: {ErrorMessage}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during session cleanup");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Session cleanup background service is stopping");
        await base.StopAsync(cancellationToken);
    }
}

/// <summary>
/// 数据库维护后台服务
/// </summary>
public class DatabaseMaintenanceBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseMaintenanceBackgroundService> _logger;
    private readonly TimeSpan _maintenanceInterval = TimeSpan.FromHours(6); // 每6小时执行一次维护

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    public DatabaseMaintenanceBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<DatabaseMaintenanceBackgroundService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Database maintenance background service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformMaintenanceAsync(stoppingToken);
                
                _logger.LogTrace("Next database maintenance scheduled in {Interval} hours", _maintenanceInterval.TotalHours);
                await Task.Delay(_maintenanceInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during database maintenance");
                
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        _logger.LogInformation("Database maintenance background service stopped");
    }

    /// <summary>
    /// 执行数据库维护任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>维护任务</returns>
    private async Task PerformMaintenanceAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MqttBrokerDbContext>();

        var startTime = DateTime.UtcNow;
        _logger.LogDebug("Starting database maintenance at {StartTime}", startTime);

        try
        {
            // 检查数据库连接
            var canConnect = await context.CanConnectAsync(cancellationToken);
            if (!canConnect)
            {
                _logger.LogWarning("Cannot connect to database during maintenance");
                return;
            }

            // 执行 SQLite 特定的维护任务
            if (context.Database.IsSqlite())
            {
                await PerformSqliteMaintenanceAsync(context, cancellationToken);
            }

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Database maintenance completed in {Duration}ms", duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database maintenance");
        }
    }

    /// <summary>
    /// 执行 SQLite 特定的维护任务
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>维护任务</returns>
    private async Task PerformSqliteMaintenanceAsync(MqttBrokerDbContext context, CancellationToken cancellationToken)
    {
        try
        {
            // 执行 VACUUM 命令以优化数据库
            await context.Database.ExecuteSqlRawAsync("VACUUM;", cancellationToken);
            _logger.LogTrace("SQLite VACUUM completed");

            // 执行 ANALYZE 命令以更新统计信息
            await context.Database.ExecuteSqlRawAsync("ANALYZE;", cancellationToken);
            _logger.LogTrace("SQLite ANALYZE completed");

            // 检查数据库完整性
            var integrityResult = await context.Database.SqlQueryRaw<string>("PRAGMA integrity_check;").ToListAsync(cancellationToken);
            if (integrityResult.Any() && integrityResult[0] != "ok")
            {
                _logger.LogWarning("Database integrity check failed: {Result}", string.Join(", ", integrityResult));
            }
            else
            {
                _logger.LogTrace("Database integrity check passed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during SQLite maintenance");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Database maintenance background service is stopping");
        await base.StopAsync(cancellationToken);
    }
}
