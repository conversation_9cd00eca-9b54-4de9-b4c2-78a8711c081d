using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;

namespace MqttBroker.Core.Session.Examples;

/// <summary>
/// 会话持久化使用示例
/// </summary>
public class SessionPersistenceExample
{
    private readonly ISessionManager _sessionManager;
    private readonly ISessionPersistence _sessionPersistence;
    private readonly ILogger<SessionPersistenceExample> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sessionManager">会话管理器</param>
    /// <param name="sessionPersistence">会话持久化服务</param>
    /// <param name="logger">日志记录器</param>
    public SessionPersistenceExample(
        ISessionManager sessionManager,
        ISessionPersistence sessionPersistence,
        ILogger<SessionPersistenceExample> logger)
    {
        _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
        _sessionPersistence = sessionPersistence ?? throw new ArgumentNullException(nameof(sessionPersistence));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 运行完整的会话持久化示例
    /// </summary>
    /// <returns>示例任务</returns>
    public async Task RunCompleteExampleAsync()
    {
        _logger.LogInformation("=== MQTT Broker 会话持久化完整示例 ===");

        try
        {
            // 1. 演示客户端连接和会话创建
            await DemonstrateClientConnectionAsync();

            // 2. 演示会话恢复
            await DemonstrateSessionRestoreAsync();

            // 3. 演示订阅持久化
            await DemonstrateSubscriptionPersistenceAsync();

            // 4. 演示未确认消息持久化
            await DemonstratePendingMessagePersistenceAsync();

            // 5. 演示会话清理
            await DemonstrateSessionCleanupAsync();

            // 6. 演示统计信息
            await DemonstrateStatisticsAsync();

            _logger.LogInformation("=== 会话持久化示例完成 ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "会话持久化示例执行失败");
        }
    }

    /// <summary>
    /// 演示客户端连接和会话创建
    /// </summary>
    private async Task DemonstrateClientConnectionAsync()
    {
        _logger.LogInformation("--- 演示客户端连接和会话创建 ---");

        // 创建模拟客户端
        var client = new MockMqttClient("persistent-client-001");
        
        // 创建 CONNECT 数据包（持久会话）
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "persistent-client-001",
            CleanSession = false, // 持久会话
            KeepAlive = 60,
            Username = "test-user",
            WillMessage = new MqttWillMessage
            {
                Topic = "clients/persistent-client-001/status",
                Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                Retain = true
            }
        };

        // 处理客户端连接
        var result = await _sessionManager.HandleClientConnectAsync(client, connectPacket);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("✅ 客户端连接成功: {ClientId}, 新会话: {IsNew}", 
                client.ClientId, result.IsNewSession);
            _logger.LogInformation("   会话信息: 协议版本={ProtocolVersion}, 保活间隔={KeepAlive}秒", 
                result.Session?.ProtocolVersion, result.Session?.KeepAliveInterval);
        }
        else
        {
            _logger.LogError("❌ 客户端连接失败: {Error}", result.ErrorMessage);
        }
    }

    /// <summary>
    /// 演示会话恢复
    /// </summary>
    private async Task DemonstrateSessionRestoreAsync()
    {
        _logger.LogInformation("--- 演示会话恢复 ---");

        var clientId = "persistent-client-001";

        // 模拟客户端断开连接（持久会话）
        var disconnectResult = await _sessionManager.HandleClientDisconnectAsync(clientId, false);
        if (disconnectResult.IsSuccess)
        {
            _logger.LogInformation("✅ 客户端断开连接: {ClientId}, 会话状态: {State}", 
                clientId, disconnectResult.Session?.State);
        }

        // 模拟客户端重新连接
        var client = new MockMqttClient(clientId);
        var connectPacket = new MqttConnectPacket
        {
            ClientId = clientId,
            CleanSession = false,
            KeepAlive = 60
        };

        var reconnectResult = await _sessionManager.HandleClientConnectAsync(client, connectPacket);
        if (reconnectResult.IsSuccess)
        {
            _logger.LogInformation("✅ 客户端重新连接成功: {ClientId}, 会话恢复: {Restored}", 
                clientId, reconnectResult.SessionRestored);
            _logger.LogInformation("   恢复的订阅数: {SubscriptionCount}, 恢复的消息数: {MessageCount}",
                reconnectResult.RestoredSubscriptionsCount, reconnectResult.RestoredPendingMessagesCount);
        }
    }

    /// <summary>
    /// 演示订阅持久化
    /// </summary>
    private async Task DemonstrateSubscriptionPersistenceAsync()
    {
        _logger.LogInformation("--- 演示订阅持久化 ---");

        var clientId = "persistent-client-001";

        // 创建测试订阅
        var subscriptions = new List<ClientSubscription>
        {
            new ClientSubscription
            {
                ClientId = clientId,
                TopicFilter = "sensors/+/temperature",
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions { NoLocal = false }
            },
            new ClientSubscription
            {
                ClientId = clientId,
                TopicFilter = "devices/+/status",
                QoSLevel = MqttQoSLevel.ExactlyOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions { RetainAsPublished = true }
            }
        };

        // 保存订阅
        var saveResult = await _sessionPersistence.SaveSubscriptionsAsync(clientId, subscriptions);
        if (saveResult.IsSuccess)
        {
            _logger.LogInformation("✅ 订阅保存成功: {Count} 个订阅", subscriptions.Count);
        }

        // 获取订阅
        var retrievedSubscriptions = await _sessionPersistence.GetSubscriptionsAsync(clientId);
        _logger.LogInformation("✅ 订阅检索成功: {Count} 个订阅", retrievedSubscriptions.Count());

        foreach (var subscription in retrievedSubscriptions)
        {
            _logger.LogInformation("   订阅: {TopicFilter}, QoS: {QoS}, 时间: {Time}",
                subscription.TopicFilter, subscription.QoSLevel, subscription.SubscribedAt);
        }
    }

    /// <summary>
    /// 演示未确认消息持久化
    /// </summary>
    private async Task DemonstratePendingMessagePersistenceAsync()
    {
        _logger.LogInformation("--- 演示未确认消息持久化 ---");

        var clientId = "persistent-client-001";

        // 创建测试未确认消息
        var publishPacket = MqttPublishPacket.Create(
            "sensors/room1/temperature", 
            System.Text.Encoding.UTF8.GetBytes("23.5"), 
            MqttQoSLevel.AtLeastOnce, 
            false, 
            1001);

        var pendingMessage = new PendingMessage
        {
            ClientId = clientId,
            MessageId = 1001,
            PublishPacket = publishPacket,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            CreatedAt = DateTime.UtcNow,
            RetransmissionCount = 0
        };

        // 保存未确认消息
        var saveResult = await _sessionPersistence.SavePendingMessageAsync(clientId, pendingMessage);
        if (saveResult.IsSuccess)
        {
            _logger.LogInformation("✅ 未确认消息保存成功: MessageId={MessageId}", pendingMessage.MessageId);
        }

        // 获取未确认消息
        var retrievedMessages = await _sessionPersistence.GetPendingMessagesAsync(clientId);
        _logger.LogInformation("✅ 未确认消息检索成功: {Count} 条消息", retrievedMessages.Count());

        foreach (var message in retrievedMessages)
        {
            _logger.LogInformation("   消息: ID={MessageId}, 主题={Topic}, QoS={QoS}, 年龄={Age}ms",
                message.MessageId, message.PublishPacket.Topic, message.QoSLevel, message.Age.TotalMilliseconds);
        }

        // 删除未确认消息
        var deleteResult = await _sessionPersistence.DeletePendingMessageAsync(clientId, 1001);
        if (deleteResult.IsSuccess)
        {
            _logger.LogInformation("✅ 未确认消息删除成功: MessageId=1001");
        }
    }

    /// <summary>
    /// 演示会话清理
    /// </summary>
    private async Task DemonstrateSessionCleanupAsync()
    {
        _logger.LogInformation("--- 演示会话清理 ---");

        // 创建过期会话
        var expiredSession = new MqttSession
        {
            ClientId = "expired-client-001",
            IsCleanSession = false,
            CreatedAt = DateTime.UtcNow.AddDays(-2),
            LastActivity = DateTime.UtcNow.AddDays(-2),
            ExpiresAt = DateTime.UtcNow.AddHours(-1), // 1小时前过期
            State = SessionState.Offline
        };

        await _sessionPersistence.CreateOrUpdateSessionAsync(expiredSession);
        _logger.LogInformation("✅ 创建过期会话: {ClientId}", expiredSession.ClientId);

        // 执行清理
        var cleanupResult = await _sessionManager.CleanupExpiredSessionsAsync();
        if (cleanupResult.IsSuccess)
        {
            _logger.LogInformation("✅ 会话清理完成: 清理了 {SessionCount} 个会话, {SubscriptionCount} 个订阅, {MessageCount} 条消息",
                cleanupResult.CleanedSessionsCount,
                cleanupResult.CleanedSubscriptionsCount,
                cleanupResult.CleanedPendingMessagesCount);
            _logger.LogInformation("   清理耗时: {Duration}ms", cleanupResult.Duration.TotalMilliseconds);
        }
        else
        {
            _logger.LogError("❌ 会话清理失败: {Error}", cleanupResult.ErrorMessage);
        }
    }

    /// <summary>
    /// 演示统计信息
    /// </summary>
    private async Task DemonstrateStatisticsAsync()
    {
        _logger.LogInformation("--- 演示统计信息 ---");

        var statistics = await _sessionManager.GetStatisticsAsync();
        
        _logger.LogInformation("✅ 会话统计信息:");
        _logger.LogInformation("   总会话数: {TotalSessions}", statistics.TotalSessions);
        _logger.LogInformation("   活跃会话数: {ActiveSessions}", statistics.ActiveSessions);
        _logger.LogInformation("   离线会话数: {OfflineSessions}", statistics.OfflineSessions);
        _logger.LogInformation("   持久会话数: {PersistentSessions}", statistics.PersistentSessions);
        _logger.LogInformation("   总订阅数: {TotalSubscriptions}", statistics.TotalSubscriptions);
        _logger.LogInformation("   总未确认消息数: {TotalPendingMessages}", statistics.TotalPendingMessages);
        _logger.LogInformation("   平均会话年龄: {AverageAge:F2} 小时", statistics.AverageSessionAgeHours);
        _logger.LogInformation("   在线会话比例: {OnlinePercentage:F1}%", statistics.OnlineSessionPercentage);
        _logger.LogInformation("   持久会话比例: {PersistentPercentage:F1}%", statistics.PersistentSessionPercentage);

        if (statistics.SessionsByProtocolVersion.Any())
        {
            _logger.LogInformation("   按协议版本分布:");
            foreach (var kvp in statistics.SessionsByProtocolVersion)
            {
                _logger.LogInformation("     {ProtocolVersion}: {Count} 个会话", kvp.Key, kvp.Value);
            }
        }
    }
}

/// <summary>
/// 模拟 MQTT 客户端（用于示例）
/// </summary>
internal class MockMqttClient : IMqttClient
{
    public string ClientId { get; }
    public IClientConnection Connection => throw new NotImplementedException();
    public MqttProtocolVersion ProtocolVersion => MqttProtocolVersion.Version311;
    public bool IsAuthenticated => true;
    public bool CleanSession => false;
    public ushort KeepAliveInterval => 60;
    public DateTime ConnectedAt => DateTime.UtcNow;
    public DateTime LastActivity => DateTime.UtcNow;
    public string? Username => "test-user";
    public MqttWillMessage? WillMessage => null;
    public MqttClientState State => MqttClientState.Authenticated;
    public IDictionary<string, object> Properties => new Dictionary<string, object>();

    public event EventHandler<MqttPacketReceivedEventArgs>? PacketReceived;
    public event EventHandler<MqttClientStateChangedEventArgs>? StateChanged;

    public MockMqttClient(string clientId)
    {
        ClientId = clientId;
    }

    public Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task HandlePacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task DisconnectAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public void UpdateLastActivity() { }

    public bool IsTimeout() => false;

    public void Dispose() { }
}
