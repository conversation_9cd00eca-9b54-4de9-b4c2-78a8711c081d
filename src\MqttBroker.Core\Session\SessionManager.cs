using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;
using System.Collections.Concurrent;

namespace MqttBroker.Core.Session;

/// <summary>
/// 会话管理器实现
/// </summary>
public class SessionManager : ISessionManager, IDisposable
{
    private readonly ISessionPersistence _sessionPersistence;
    private readonly ITopicSubscriptionManager _subscriptionManager;
    private readonly IQoSManager _qosManager;
    private readonly ILogger<SessionManager> _logger;
    private readonly SessionManagerOptions _options;
    private readonly Timer? _cleanupTimer;
    private readonly ConcurrentDictionary<string, object> _sessionLocks = new();
    private bool _disposed;

    /// <summary>
    /// 会话状态变更事件
    /// </summary>
    public event EventHandler<SessionStateChangedEventArgs>? SessionStateChanged;

    /// <summary>
    /// 会话过期事件
    /// </summary>
    public event EventHandler<SessionExpiredEventArgs>? SessionExpired;

    /// <summary>
    /// 会话恢复事件
    /// </summary>
    public event EventHandler<SessionRestoredEventArgs>? SessionRestored;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sessionPersistence">会话持久化服务</param>
    /// <param name="options">配置选项</param>
    /// <param name="logger">日志记录器</param>
    public SessionManager(
        ISessionPersistence sessionPersistence,
        IOptions<SessionManagerOptions> options,
        ILogger<SessionManager> logger)
    {
        _sessionPersistence = sessionPersistence ?? throw new ArgumentNullException(nameof(sessionPersistence));
        _subscriptionManager = null!; // 暂时设为 null，实际使用时需要注入
        _qosManager = null!; // 暂时设为 null，实际使用时需要注入
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 启动清理定时器
        if (_options.EnableAutomaticCleanup)
        {
            _cleanupTimer = new Timer(PerformCleanup, null,
                TimeSpan.FromMinutes(_options.CleanupIntervalMinutes),
                TimeSpan.FromMinutes(_options.CleanupIntervalMinutes));
        }
    }

    /// <summary>
    /// 处理客户端连接，创建或恢复会话
    /// </summary>
    public async Task<SessionHandleResult> HandleClientConnectAsync(IMqttClient client, MqttConnectPacket connectPacket, CancellationToken cancellationToken = default)
    {
        try
        {
            if (client == null)
                throw new ArgumentNullException(nameof(client));

            if (connectPacket == null)
                throw new ArgumentNullException(nameof(connectPacket));

            var clientId = client.ClientId;
            var lockObject = _sessionLocks.GetOrAdd(clientId, _ => new object());

            lock (lockObject)
            {
                return HandleClientConnectInternal(client, connectPacket, cancellationToken).GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client connect for: {ClientId}", client?.ClientId);
            return SessionHandleResult.Failure(ex.Message);
        }
    }

    private async Task<SessionHandleResult> HandleClientConnectInternal(IMqttClient client, MqttConnectPacket connectPacket, CancellationToken cancellationToken)
    {
        var clientId = client.ClientId;
        var isCleanSession = connectPacket.CleanSession;

        // 检查是否存在现有会话
        var existingSession = await _sessionPersistence.GetSessionAsync(clientId, cancellationToken);
        bool isNewSession = existingSession == null;
        bool sessionRestored = false;

        MqttSession session;

        if (isCleanSession || existingSession == null)
        {
            // Clean Session 或新会话：创建新会话
            session = CreateNewSession(client, connectPacket);
            
            if (existingSession != null)
            {
                // 清理旧会话数据
                await _sessionPersistence.DeleteSessionAsync(clientId, cancellationToken);
                _logger.LogDebug("Cleaned existing session for Clean Session client: {ClientId}", clientId);
            }
        }
        else
        {
            // 持久会话：恢复现有会话
            session = existingSession;
            session.IsOnline = true;
            session.ConnectedAt = DateTime.UtcNow;
            session.LastActivity = DateTime.UtcNow;
            session.State = SessionState.Active;
            
            sessionRestored = true;
            _logger.LogDebug("Restored existing session for client: {ClientId}", clientId);
        }

        // 保存会话
        var saveResult = await _sessionPersistence.CreateOrUpdateSessionAsync(session, cancellationToken);
        if (!saveResult.IsSuccess)
        {
            _logger.LogError("Failed to save session for client: {ClientId}, error: {Error}", 
                clientId, saveResult.ErrorMessage);
            return SessionHandleResult.Failure(saveResult.ErrorMessage ?? "Failed to save session");
        }

        var result = SessionHandleResult.Success(session, isNewSession, sessionRestored);

        // 如果是恢复的会话，恢复订阅和未确认消息
        if (sessionRestored && !isCleanSession)
        {
            try
            {
                var restoreResult = await RestoreSessionAsync(client, cancellationToken);
                if (restoreResult.IsSuccess)
                {
                    result.RestoredSubscriptionsCount = restoreResult.RestoredSubscriptions.Count;
                    result.RestoredPendingMessagesCount = restoreResult.RestoredPendingMessages.Count;

                    // 触发会话恢复事件
                    SessionRestored?.Invoke(this, new SessionRestoredEventArgs
                    {
                        ClientId = clientId,
                        RestoredSession = session,
                        RestoreResult = restoreResult
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to restore session for client: {ClientId}", clientId);
            }
        }

        // 触发会话状态变更事件
        SessionStateChanged?.Invoke(this, new SessionStateChangedEventArgs
        {
            ClientId = clientId,
            OldState = existingSession?.State ?? SessionState.Active,
            NewState = SessionState.Active,
            Reason = isNewSession ? "New session created" : "Session restored"
        });

        _logger.LogInformation("Session handled for client: {ClientId}, new: {IsNew}, restored: {Restored}", 
            clientId, isNewSession, sessionRestored);

        return result;
    }

    /// <summary>
    /// 处理客户端断开连接
    /// </summary>
    public async Task<SessionHandleResult> HandleClientDisconnectAsync(string clientId, bool cleanSession, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            var session = await _sessionPersistence.GetSessionAsync(clientId, cancellationToken);
            if (session == null)
            {
                _logger.LogWarning("Session not found for disconnecting client: {ClientId}", clientId);
                return SessionHandleResult.Success(new MqttSession { ClientId = clientId }, false, false);
            }

            if (cleanSession)
            {
                // Clean Session：删除会话
                await _sessionPersistence.DeleteSessionAsync(clientId, cancellationToken);

                // 清理 QoS 管理器中的消息（如果可用）
                if (_qosManager != null)
                {
                    try
                    {
                        await _qosManager.CleanupClientMessagesAsync(clientId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to cleanup QoS messages for client: {ClientId}", clientId);
                    }
                }

                session.State = SessionState.Deleted;
                _logger.LogDebug("Deleted Clean Session for client: {ClientId}", clientId);
            }
            else
            {
                // 持久会话：标记为离线
                session.IsOnline = false;
                session.DisconnectedAt = DateTime.UtcNow;
                session.State = SessionState.Offline;
                
                // 设置过期时间
                if (_options.SessionExpirationHours > 0)
                {
                    session.ExpiresAt = DateTime.UtcNow.AddHours(_options.SessionExpirationHours);
                }

                await _sessionPersistence.CreateOrUpdateSessionAsync(session, cancellationToken);
                _logger.LogDebug("Marked session as offline for client: {ClientId}", clientId);
            }

            // 触发会话状态变更事件
            SessionStateChanged?.Invoke(this, new SessionStateChangedEventArgs
            {
                ClientId = clientId,
                OldState = SessionState.Active,
                NewState = session.State,
                Reason = "Client disconnected"
            });

            return SessionHandleResult.Success(session, false, false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client disconnect for: {ClientId}", clientId);
            return SessionHandleResult.Failure(ex.Message);
        }
    }

    /// <summary>
    /// 恢复客户端会话状态
    /// </summary>
    public async Task<SessionRestoreResult> RestoreSessionAsync(IMqttClient client, CancellationToken cancellationToken = default)
    {
        try
        {
            if (client == null)
                throw new ArgumentNullException(nameof(client));

            var clientId = client.ClientId;

            // 恢复订阅
            var subscriptions = await _sessionPersistence.GetSubscriptionsAsync(clientId, cancellationToken);
            var subscriptionList = subscriptions.ToList();

            // 将订阅添加到订阅管理器（如果可用）
            if (_subscriptionManager != null)
            {
                foreach (var subscription in subscriptionList)
                {
                    try
                    {
                        var mqttSubscription = new MqttSubscription
                        {
                            TopicFilter = subscription.TopicFilter,
                            QoSLevel = subscription.QoSLevel,
                            Options = subscription.Options
                        };

                        await _subscriptionManager.SubscribeAsync(client, mqttSubscription);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to restore subscription {TopicFilter} for client: {ClientId}",
                            subscription.TopicFilter, clientId);
                    }
                }
            }

            // 恢复未确认消息
            var pendingMessages = await _sessionPersistence.GetPendingMessagesAsync(clientId, cancellationToken);
            var pendingMessageList = pendingMessages.ToList();

            // 将未确认消息添加到 QoS 管理器（如果可用）
            if (_qosManager != null)
            {
                foreach (var pendingMessage in pendingMessageList)
                {
                    try
                    {
                        await _qosManager.RestorePendingMessageAsync(client, pendingMessage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to restore pending message {MessageId} for client: {ClientId}",
                            pendingMessage.MessageId, clientId);
                    }
                }
            }

            _logger.LogInformation("Restored session for client: {ClientId}, subscriptions: {SubscriptionCount}, pending messages: {MessageCount}",
                clientId, subscriptionList.Count, pendingMessageList.Count);

            return SessionRestoreResult.Success(subscriptionList, pendingMessageList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring session for client: {ClientId}", client?.ClientId);
            return SessionRestoreResult.Failure(ex.Message);
        }
    }

    /// <summary>
    /// 保存客户端会话状态
    /// </summary>
    public async Task<SessionOperationResult> SaveSessionAsync(IMqttClient client, CancellationToken cancellationToken = default)
    {
        try
        {
            if (client == null)
                throw new ArgumentNullException(nameof(client));

            var clientId = client.ClientId;

            // 创建会话对象
            var session = CreateSessionFromClient(client);

            // 保存会话
            var result = await _sessionPersistence.CreateOrUpdateSessionAsync(session, cancellationToken);

            if (result.IsSuccess)
            {
                // 保存订阅信息
                var subscriptions = await _subscriptionManager.GetClientSubscriptionsAsync(clientId);
                await _sessionPersistence.SaveSubscriptionsAsync(clientId, subscriptions, cancellationToken);

                // 保存未确认消息
                var pendingMessages = await _qosManager.GetClientPendingMessagesAsync(clientId);
                foreach (var pendingMessage in pendingMessages)
                {
                    await _sessionPersistence.SavePendingMessageAsync(clientId, pendingMessage, cancellationToken);
                }

                _logger.LogDebug("Saved session state for client: {ClientId}", clientId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving session for client: {ClientId}", client?.ClientId);
            return SessionOperationResult.Failure(SessionOperationType.Update, ex.Message, client?.ClientId);
        }
    }

    /// <summary>
    /// 更新会话活动时间
    /// </summary>
    public async Task<SessionOperationResult> UpdateSessionActivityAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            return await _sessionPersistence.UpdateSessionActivityAsync(clientId, DateTime.UtcNow, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating session activity for client: {ClientId}", clientId);
            return SessionOperationResult.Failure(SessionOperationType.UpdateActivity, ex.Message, clientId);
        }
    }

    /// <summary>
    /// 获取会话信息
    /// </summary>
    public async Task<MqttSession?> GetSessionAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sessionPersistence.GetSessionAsync(clientId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting session for client: {ClientId}", clientId);
            return null;
        }
    }

    /// <summary>
    /// 检查会话是否存在
    /// </summary>
    public async Task<bool> SessionExistsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sessionPersistence.SessionExistsAsync(clientId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking session existence for client: {ClientId}", clientId);
            return false;
        }
    }

    /// <summary>
    /// 清理过期会话
    /// </summary>
    public async Task<SessionCleanupResult> CleanupExpiredSessionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var expiredBefore = DateTime.UtcNow.AddHours(-_options.SessionExpirationHours);
            var result = await _sessionPersistence.CleanupExpiredSessionsAsync(expiredBefore, cancellationToken);

            if (result.IsSuccess && result.CleanedSessionsCount > 0)
            {
                _logger.LogInformation("Cleaned up {SessionCount} expired sessions", result.CleanedSessionsCount);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired sessions");
            return SessionCleanupResult.Failure(ex.Message);
        }
    }

    /// <summary>
    /// 获取会话统计信息
    /// </summary>
    public async Task<SessionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sessionPersistence.GetStatisticsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting session statistics");
            return new SessionStatistics();
        }
    }

    /// <summary>
    /// 启动会话管理器
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Session manager started");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止会话管理器
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken = default)
    {
        _cleanupTimer?.Dispose();
        _logger.LogInformation("Session manager stopped");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 创建新会话
    /// </summary>
    private MqttSession CreateNewSession(IMqttClient client, MqttConnectPacket connectPacket)
    {
        return new MqttSession
        {
            ClientId = client.ClientId,
            IsCleanSession = connectPacket.CleanSession,
            CreatedAt = DateTime.UtcNow,
            LastActivity = DateTime.UtcNow,
            ProtocolVersion = connectPacket.ProtocolVersion,
            KeepAliveInterval = connectPacket.KeepAlive,
            Username = connectPacket.Username,
            WillMessage = connectPacket.WillMessage,
            IsOnline = true,
            ConnectedAt = DateTime.UtcNow,
            State = SessionState.Active,
            ExpiresAt = connectPacket.CleanSession ? null : DateTime.UtcNow.AddHours(_options.SessionExpirationHours)
        };
    }

    /// <summary>
    /// 从客户端创建会话对象
    /// </summary>
    private MqttSession CreateSessionFromClient(IMqttClient client)
    {
        return new MqttSession
        {
            ClientId = client.ClientId,
            IsCleanSession = client.CleanSession,
            LastActivity = DateTime.UtcNow,
            ProtocolVersion = client.ProtocolVersion,
            KeepAliveInterval = client.KeepAliveInterval,
            Username = client.Username,
            WillMessage = client.WillMessage,
            IsOnline = true,
            State = SessionState.Active
        };
    }

    /// <summary>
    /// 执行清理任务
    /// </summary>
    private async void PerformCleanup(object? state)
    {
        try
        {
            await CleanupExpiredSessionsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during automatic cleanup");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _disposed = true;
        }
    }
}
