namespace MqttBroker.Core.Session;

/// <summary>
/// 会话操作结果
/// </summary>
public class SessionOperationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 受影响的记录数
    /// </summary>
    public int AffectedRecords { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public SessionOperationType OperationType { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 额外信息
    /// </summary>
    public Dictionary<string, object>? AdditionalInfo { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="operationType">操作类型</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="affectedRecords">受影响的记录数</param>
    /// <returns>操作结果</returns>
    public static SessionOperationResult Success(SessionOperationType operationType, string? clientId = null, int affectedRecords = 1)
    {
        return new SessionOperationResult
        {
            IsSuccess = true,
            OperationType = operationType,
            ClientId = clientId,
            AffectedRecords = affectedRecords
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="operationType">操作类型</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>操作结果</returns>
    public static SessionOperationResult Failure(SessionOperationType operationType, string errorMessage, string? clientId = null)
    {
        return new SessionOperationResult
        {
            IsSuccess = false,
            OperationType = operationType,
            ErrorMessage = errorMessage,
            ClientId = clientId,
            AffectedRecords = 0
        };
    }
}

/// <summary>
/// 批量会话操作结果
/// </summary>
public class BatchSessionOperationResult
{
    /// <summary>
    /// 是否全部成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 总操作数
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// 成功操作数
    /// </summary>
    public int SuccessfulOperations { get; set; }

    /// <summary>
    /// 失败操作数
    /// </summary>
    public int FailedOperations { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 操作类型
    /// </summary>
    public SessionOperationType OperationType { get; set; }

    /// <summary>
    /// 失败的操作详情
    /// </summary>
    public List<SessionOperationResult> FailedOperations { get; set; } = new();

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="operationType">操作类型</param>
    /// <param name="totalOperations">总操作数</param>
    /// <param name="successfulOperations">成功操作数</param>
    /// <returns>批量操作结果</returns>
    public static BatchSessionOperationResult Success(SessionOperationType operationType, int totalOperations, int successfulOperations)
    {
        return new BatchSessionOperationResult
        {
            IsSuccess = successfulOperations == totalOperations,
            OperationType = operationType,
            TotalOperations = totalOperations,
            SuccessfulOperations = successfulOperations,
            FailedOperations = totalOperations - successfulOperations
        };
    }
}

/// <summary>
/// 会话清理结果
/// </summary>
public class SessionCleanupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 清理的会话数量
    /// </summary>
    public int CleanedSessionsCount { get; set; }

    /// <summary>
    /// 清理的订阅数量
    /// </summary>
    public int CleanedSubscriptionsCount { get; set; }

    /// <summary>
    /// 清理的未确认消息数量
    /// </summary>
    public int CleanedPendingMessagesCount { get; set; }

    /// <summary>
    /// 清理开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 清理结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 清理耗时
    /// </summary>
    public TimeSpan Duration => EndTime.HasValue ? EndTime.Value - StartTime : TimeSpan.Zero;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 清理的总记录数
    /// </summary>
    public int TotalCleanedRecords => CleanedSessionsCount + CleanedSubscriptionsCount + CleanedPendingMessagesCount;

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="sessionsCount">清理的会话数量</param>
    /// <param name="subscriptionsCount">清理的订阅数量</param>
    /// <param name="pendingMessagesCount">清理的未确认消息数量</param>
    /// <returns>清理结果</returns>
    public static SessionCleanupResult Success(int sessionsCount, int subscriptionsCount, int pendingMessagesCount)
    {
        return new SessionCleanupResult
        {
            IsSuccess = true,
            CleanedSessionsCount = sessionsCount,
            CleanedSubscriptionsCount = subscriptionsCount,
            CleanedPendingMessagesCount = pendingMessagesCount,
            EndTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>清理结果</returns>
    public static SessionCleanupResult Failure(string errorMessage)
    {
        return new SessionCleanupResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            EndTime = DateTime.UtcNow
        };
    }
}

/// <summary>
/// 会话统计信息
/// </summary>
public class SessionStatistics
{
    /// <summary>
    /// 总会话数
    /// </summary>
    public long TotalSessions { get; set; }

    /// <summary>
    /// 活跃会话数
    /// </summary>
    public long ActiveSessions { get; set; }

    /// <summary>
    /// 离线会话数
    /// </summary>
    public long OfflineSessions { get; set; }

    /// <summary>
    /// 过期会话数
    /// </summary>
    public long ExpiredSessions { get; set; }

    /// <summary>
    /// Clean Session 数量
    /// </summary>
    public long CleanSessions { get; set; }

    /// <summary>
    /// 持久会话数量
    /// </summary>
    public long PersistentSessions { get; set; }

    /// <summary>
    /// 总订阅数
    /// </summary>
    public long TotalSubscriptions { get; set; }

    /// <summary>
    /// 总未确认消息数
    /// </summary>
    public long TotalPendingMessages { get; set; }

    /// <summary>
    /// 平均会话年龄（小时）
    /// </summary>
    public double AverageSessionAgeHours { get; set; }

    /// <summary>
    /// 最老会话年龄（小时）
    /// </summary>
    public double OldestSessionAgeHours { get; set; }

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 按协议版本分组的会话数
    /// </summary>
    public Dictionary<MqttProtocolVersion, long> SessionsByProtocolVersion { get; set; } = new();

    /// <summary>
    /// 内存使用情况（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// 在线会话比例
    /// </summary>
    public double OnlineSessionPercentage => TotalSessions > 0 ? (double)ActiveSessions / TotalSessions * 100 : 0;

    /// <summary>
    /// 持久会话比例
    /// </summary>
    public double PersistentSessionPercentage => TotalSessions > 0 ? (double)PersistentSessions / TotalSessions * 100 : 0;
}

/// <summary>
/// 会话操作类型枚举
/// </summary>
public enum SessionOperationType
{
    /// <summary>
    /// 创建会话
    /// </summary>
    Create,

    /// <summary>
    /// 更新会话
    /// </summary>
    Update,

    /// <summary>
    /// 删除会话
    /// </summary>
    Delete,

    /// <summary>
    /// 查询会话
    /// </summary>
    Query,

    /// <summary>
    /// 保存订阅
    /// </summary>
    SaveSubscriptions,

    /// <summary>
    /// 保存未确认消息
    /// </summary>
    SavePendingMessage,

    /// <summary>
    /// 删除未确认消息
    /// </summary>
    DeletePendingMessage,

    /// <summary>
    /// 清理过期会话
    /// </summary>
    Cleanup,

    /// <summary>
    /// 批量删除
    /// </summary>
    BatchDelete,

    /// <summary>
    /// 更新活动时间
    /// </summary>
    UpdateActivity
}
